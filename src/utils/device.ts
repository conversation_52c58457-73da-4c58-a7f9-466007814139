import { ref, computed, readonly, onMounted, onUnmounted } from 'vue'
import type { DeviceType } from '@/types'

/**
 * 检测设备类型
 */
export function detectDeviceType(): DeviceType {
  const userAgent = navigator.userAgent.toLowerCase()
  const isMobile = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent)
  
  // 也可以通过屏幕尺寸判断
  const isSmallScreen = window.innerWidth <= 768
  
  return isMobile || isSmallScreen ? 'mobile' : 'desktop'
}

/**
 * 监听设备类型变化
 */
export function useDeviceType() {
  const deviceType = ref<DeviceType>(detectDeviceType())

  const updateDeviceType = () => {
    deviceType.value = detectDeviceType()
  }

  onMounted(() => {
    window.addEventListener('resize', updateDeviceType)
  })

  onUnmounted(() => {
    window.removeEventListener('resize', updateDeviceType)
  })

  return {
    deviceType: readonly(deviceType),
    isMobile: computed(() => deviceType.value === 'mobile'),
    isDesktop: computed(() => deviceType.value === 'desktop')
  }
}

/**
 * 移动端布局高度计算
 */
export function useMobileLayout() {
  const screenHeight = ref(window.innerHeight)
  const safeAreaBottom = ref(0)

  // 获取安全区域底部高度
  const getSafeAreaBottom = () => {
    const style = getComputedStyle(document.documentElement)
    const safeAreaInsetBottom = style.getPropertyValue('env(safe-area-inset-bottom)')
    return safeAreaInsetBottom ? parseInt(safeAreaInsetBottom) : 0
  }

  // 更新屏幕尺寸
  const updateScreenSize = () => {
    screenHeight.value = window.innerHeight
    safeAreaBottom.value = getSafeAreaBottom()
  }

  onMounted(() => {
    updateScreenSize()
    window.addEventListener('resize', updateScreenSize)
    window.addEventListener('orientationchange', updateScreenSize)
  })

  onUnmounted(() => {
    window.removeEventListener('resize', updateScreenSize)
    window.removeEventListener('orientationchange', updateScreenSize)
  })

  // 计算各区域高度
  const layoutHeights = computed(() => {
    const totalHeight = screenHeight.value
    const bottomNavHeight = 80 + safeAreaBottom.value // 底部导航栏高度 + 安全区域
    const availableHeight = totalHeight - bottomNavHeight

    // 分配比例：日历 40%，事件列表 60%
    const calendarHeight = Math.floor(availableHeight * 0.4)
    const eventsHeight = availableHeight - calendarHeight

    return {
      totalHeight,
      availableHeight,
      calendarHeight: Math.max(calendarHeight, 300), // 最小高度300px
      eventsHeight: Math.max(eventsHeight, 200), // 最小高度200px
      bottomNavHeight
    }
  })

  return {
    screenHeight: readonly(screenHeight),
    layoutHeights: readonly(layoutHeights)
  }
}
