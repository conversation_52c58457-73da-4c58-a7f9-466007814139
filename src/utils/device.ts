import { ref, computed, readonly, onMounted, onUnmounted } from 'vue'
import type { DeviceType } from '@/types'

/**
 * 检测设备类型
 */
export function detectDeviceType(): DeviceType {
  const userAgent = navigator.userAgent.toLowerCase()
  const isMobile = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent)
  
  // 也可以通过屏幕尺寸判断
  const isSmallScreen = window.innerWidth <= 768
  
  return isMobile || isSmallScreen ? 'mobile' : 'desktop'
}

/**
 * 监听设备类型变化
 */
export function useDeviceType() {
  const deviceType = ref<DeviceType>(detectDeviceType())

  const updateDeviceType = () => {
    deviceType.value = detectDeviceType()
  }

  onMounted(() => {
    window.addEventListener('resize', updateDeviceType)
  })

  onUnmounted(() => {
    window.removeEventListener('resize', updateDeviceType)
  })

  return {
    deviceType: readonly(deviceType),
    isMobile: computed(() => deviceType.value === 'mobile'),
    isDesktop: computed(() => deviceType.value === 'desktop')
  }
}

/**
 * 移动端布局高度计算
 */
export function useMobileLayout() {
  const screenHeight = ref(window.innerHeight)
  const safeAreaBottom = ref(0)

  // 获取安全区域底部高度
  const getSafeAreaBottom = () => {
    const style = getComputedStyle(document.documentElement)
    const safeAreaInsetBottom = style.getPropertyValue('env(safe-area-inset-bottom)')
    return safeAreaInsetBottom ? parseInt(safeAreaInsetBottom) : 0
  }

  // 更新屏幕尺寸
  const updateScreenSize = () => {
    screenHeight.value = window.innerHeight
    safeAreaBottom.value = getSafeAreaBottom()
  }

  onMounted(() => {
    updateScreenSize()
    window.addEventListener('resize', updateScreenSize)
    window.addEventListener('orientationchange', updateScreenSize)
  })

  onUnmounted(() => {
    window.removeEventListener('resize', updateScreenSize)
    window.removeEventListener('orientationchange', updateScreenSize)
  })

  // 计算各区域高度
  const layoutHeights = computed(() => {
    const totalHeight = screenHeight.value
    // 底部导航栏实际高度：图标24px + 文字12px + 内边距8px + 外边距16px + 安全区域
    const bottomNavHeight = 60 + safeAreaBottom.value

    // 日历固定高度（根据屏幕大小调整）
    let calendarHeight = 350
    if (totalHeight < 700) {
      calendarHeight = 300
    } else if (totalHeight < 600) {
      calendarHeight = 280
    }

    // 事件列表高度 = 总高度 - 日历高度 - 底部功能栏高度
    const eventsHeight = totalHeight - calendarHeight - bottomNavHeight

    // 确保事件列表有最小高度
    const minEventsHeight = 150
    const finalEventsHeight = Math.max(eventsHeight, minEventsHeight)

    // 如果事件列表高度不够，适当减少日历高度
    const finalCalendarHeight = finalEventsHeight === minEventsHeight
      ? totalHeight - minEventsHeight - bottomNavHeight
      : calendarHeight

    return {
      totalHeight,
      calendarHeight: Math.max(finalCalendarHeight, 250), // 日历最小高度250px
      eventsHeight: finalEventsHeight,
      bottomNavHeight
    }
  })

  return {
    screenHeight: readonly(screenHeight),
    layoutHeights: readonly(layoutHeights)
  }
}
