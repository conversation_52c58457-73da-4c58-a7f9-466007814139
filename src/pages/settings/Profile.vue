<template>
  <div class="profile-page">
    <div class="page-header">
      <n-button quaternary @click="$router.back()">
        <n-icon :size="20">
          <ArrowBack />
        </n-icon>
      </n-button>
      <h1>个人资料</h1>
      <div></div>
    </div>
    
    <div class="page-content">
      <div class="profile-form">
        <div class="avatar-section">
          <n-avatar
            :size="80"
            :src="userStore.user?.avatar_url"
            :fallback-src="'/default-avatar.png'"
          >
            {{ userStore.user?.nickname?.[0] || userStore.user?.username[0] }}
          </n-avatar>
          <n-button size="small" @click="handleAvatarUpload">
            更换头像
          </n-button>
        </div>
        
        <n-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-placement="top"
          @submit.prevent="handleSubmit"
        >
          <n-form-item path="username" label="用户名">
            <n-input
              v-model:value="form.username"
              placeholder="用户名"
              disabled
            />
          </n-form-item>
          
          <n-form-item path="email" label="邮箱">
            <n-input
              v-model:value="form.email"
              placeholder="邮箱地址"
              disabled
            />
          </n-form-item>
          
          <n-form-item path="nickname" label="昵称">
            <n-input
              v-model:value="form.nickname"
              placeholder="请输入昵称"
            />
          </n-form-item>
          
          <n-form-item>
            <n-space>
              <n-button type="primary" attr-type="submit" :loading="loading">
                保存修改
              </n-button>
              <n-button @click="resetForm">
                重置
              </n-button>
            </n-space>
          </n-form-item>
        </n-form>
        
        <div class="account-info">
          <h3>账户信息</h3>
          <div class="info-items">
            <div class="info-item">
              <label>注册时间</label>
              <span>{{ formatDate(userStore.user?.created_at || '', 'YYYY-MM-DD HH:mm') }}</span>
            </div>
            <div class="info-item">
              <label>最后更新</label>
              <span>{{ formatDate(userStore.user?.updated_at || '', 'YYYY-MM-DD HH:mm') }}</span>
            </div>
            <div class="info-item">
              <label>账户状态</label>
              <n-tag :type="userStore.user?.is_active ? 'success' : 'error'">
                {{ userStore.user?.is_active ? '正常' : '已禁用' }}
              </n-tag>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ArrowBack } from '@vicons/ionicons5'
import type { FormInst, FormRules } from 'naive-ui'
import { useUserStore } from '@/stores/user'
import { formatDate } from '@/utils/date'

const router = useRouter()
const userStore = useUserStore()

// 表单引用
const formRef = ref<FormInst | null>(null)

// 加载状态
const loading = ref(false)

// 表单数据
const form = reactive({
  username: '',
  email: '',
  nickname: ''
})

// 表单验证规则
const rules: FormRules = {
  nickname: [
    { max: 50, message: '昵称长度不能超过50个字符', trigger: 'blur' }
  ]
}

// 初始化表单数据
const initFormData = () => {
  if (userStore.user) {
    form.username = userStore.user.username
    form.email = userStore.user.email
    form.nickname = userStore.user.nickname || ''
  }
}

// 重置表单
const resetForm = () => {
  initFormData()
}

// 处理头像上传
const handleAvatarUpload = () => {
  // 这里可以实现头像上传功能
  window.$message?.info('头像上传功能开发中...')
}

// 处理表单提交
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    loading.value = true
    
    const updateData = {
      nickname: form.nickname || undefined
    }
    
    const success = await userStore.updateUserInfo(updateData)
    if (success) {
      window.$message?.success('个人资料更新成功')
    }
  } catch (error) {
    console.error('Update profile error:', error)
  } finally {
    loading.value = false
  }
}

// 初始化
onMounted(() => {
  initFormData()
})

// 监听用户信息变化
watch(() => userStore.user, () => {
  initFormData()
}, { deep: true })
</script>

<style scoped>
.profile-page {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  border-bottom: 1px solid #e0e0e0;
  background: white;
}

.page-header h1 {
  font-size: 20px;
  font-weight: 600;
  color: #333;
}

.page-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  background: #f5f5f5;
}

.profile-form {
  max-width: 500px;
  margin: 0 auto;
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid #f0f0f0;
}

.account-info {
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
}

.account-info h3 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
}

.info-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.info-item label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.info-item span {
  font-size: 14px;
  color: #333;
}

@media (max-width: 768px) {
  .page-header {
    padding: 12px 16px;
  }
  
  .page-content {
    padding: 16px;
  }
  
  .profile-form {
    padding: 16px;
  }
}
</style>
