<template>
  <div class="settings-page">
    <div class="page-header">
      <h1>设置</h1>
    </div>
    
    <div class="page-content">
      <div class="settings-sections">
        <!-- 账户设置 -->
        <div class="settings-section">
          <h2>账户设置</h2>
          <div class="settings-items">
            <div class="settings-item" @click="$router.push('/settings/profile')">
              <div class="item-content">
                <n-icon :size="20" class="item-icon">
                  <Person />
                </n-icon>
                <div class="item-info">
                  <div class="item-title">个人资料</div>
                  <div class="item-desc">修改昵称、头像等信息</div>
                </div>
              </div>
              <n-icon :size="16" color="#ccc">
                <ChevronForward />
              </n-icon>
            </div>
            
            <div class="settings-item" @click="showChangePassword = true">
              <div class="item-content">
                <n-icon :size="20" class="item-icon">
                  <LockClosed />
                </n-icon>
                <div class="item-info">
                  <div class="item-title">修改密码</div>
                  <div class="item-desc">更改登录密码</div>
                </div>
              </div>
              <n-icon :size="16" color="#ccc">
                <ChevronForward />
              </n-icon>
            </div>
          </div>
        </div>
        
        <!-- 应用设置 -->
        <div class="settings-section">
          <h2>应用设置</h2>
          <div class="settings-items">
            <div class="settings-item">
              <div class="item-content">
                <n-icon :size="20" class="item-icon">
                  <Moon />
                </n-icon>
                <div class="item-info">
                  <div class="item-title">深色模式</div>
                  <div class="item-desc">切换应用主题</div>
                </div>
              </div>
              <n-switch v-model:value="darkMode" @update:value="handleThemeChange" />
            </div>
            
            <div class="settings-item">
              <div class="item-content">
                <n-icon :size="20" class="item-icon">
                  <Notifications />
                </n-icon>
                <div class="item-info">
                  <div class="item-title">通知设置</div>
                  <div class="item-desc">管理推送通知</div>
                </div>
              </div>
              <n-switch v-model:value="notifications" />
            </div>
          </div>
        </div>
        
        <!-- 其他 -->
        <div class="settings-section">
          <h2>其他</h2>
          <div class="settings-items">
            <div class="settings-item">
              <div class="item-content">
                <n-icon :size="20" class="item-icon">
                  <InformationCircle />
                </n-icon>
                <div class="item-info">
                  <div class="item-title">关于应用</div>
                  <div class="item-desc">版本信息和帮助</div>
                </div>
              </div>
              <n-icon :size="16" color="#ccc">
                <ChevronForward />
              </n-icon>
            </div>
            
            <div class="settings-item danger" @click="handleLogout">
              <div class="item-content">
                <n-icon :size="20" class="item-icon">
                  <LogOut />
                </n-icon>
                <div class="item-info">
                  <div class="item-title">退出登录</div>
                  <div class="item-desc">退出当前账户</div>
                </div>
              </div>
              <n-icon :size="16" color="#ccc">
                <ChevronForward />
              </n-icon>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 修改密码弹窗 -->
    <n-modal v-model:show="showChangePassword" preset="dialog" title="修改密码">
      <n-form
        ref="passwordFormRef"
        :model="passwordForm"
        :rules="passwordRules"
        label-placement="top"
      >
        <n-form-item path="oldPassword" label="当前密码">
          <n-input
            v-model:value="passwordForm.oldPassword"
            type="password"
            placeholder="请输入当前密码"
            show-password-on="mousedown"
          />
        </n-form-item>
        
        <n-form-item path="newPassword" label="新密码">
          <n-input
            v-model:value="passwordForm.newPassword"
            type="password"
            placeholder="请输入新密码"
            show-password-on="mousedown"
          />
        </n-form-item>
        
        <n-form-item path="confirmPassword" label="确认新密码">
          <n-input
            v-model:value="passwordForm.confirmPassword"
            type="password"
            placeholder="请再次输入新密码"
            show-password-on="mousedown"
          />
        </n-form-item>
      </n-form>
      
      <template #action>
        <n-space>
          <n-button @click="showChangePassword = false">取消</n-button>
          <n-button type="primary" :loading="passwordLoading" @click="handleChangePassword">
            确认修改
          </n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import {
  Person,
  LockClosed,
  Moon,
  Notifications,
  InformationCircle,
  LogOut,
  ChevronForward
} from '@vicons/ionicons5'
import type { FormInst, FormRules } from 'naive-ui'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const userStore = useUserStore()

// 表单引用
const passwordFormRef = ref<FormInst | null>(null)

// 状态
const showChangePassword = ref(false)
const passwordLoading = ref(false)
const darkMode = ref(false)
const notifications = ref(true)

// 修改密码表单
const passwordForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 密码表单验证规则
const passwordRules: FormRules = {
  oldPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule, value) => {
        return value === passwordForm.newPassword
      },
      message: '两次输入的密码不一致',
      trigger: 'blur'
    }
  ]
}

// 处理主题变化
const handleThemeChange = (value: boolean) => {
  // 这里可以实现主题切换逻辑
  console.log('Theme changed:', value)
}

// 处理修改密码
const handleChangePassword = async () => {
  if (!passwordFormRef.value) return
  
  try {
    await passwordFormRef.value.validate()
    passwordLoading.value = true
    
    const success = await userStore.changePassword(
      passwordForm.oldPassword,
      passwordForm.newPassword
    )
    
    if (success) {
      showChangePassword.value = false
      // 重置表单
      passwordForm.oldPassword = ''
      passwordForm.newPassword = ''
      passwordForm.confirmPassword = ''
    }
  } catch (error) {
    console.error('Change password error:', error)
  } finally {
    passwordLoading.value = false
  }
}

// 处理退出登录
const handleLogout = () => {
  window.$dialog?.warning({
    title: '确认退出',
    content: '确定要退出登录吗？',
    positiveText: '退出',
    negativeText: '取消',
    onPositiveClick: async () => {
      await userStore.logout()
      router.push('/login')
    }
  })
}
</script>

<style scoped>
.settings-page {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.page-header {
  padding: 24px;
  border-bottom: 1px solid #e0e0e0;
  background: white;
}

.page-header h1 {
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.page-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  background: #f5f5f5;
}

.settings-sections {
  max-width: 600px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.settings-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.settings-section h2 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  margin: 0;
}

.settings-items {
  display: flex;
  flex-direction: column;
}

.settings-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-bottom: 1px solid #f0f0f0;
}

.settings-item:last-child {
  border-bottom: none;
}

.settings-item:hover {
  background-color: #f9f9f9;
}

.settings-item.danger {
  color: #e74c3c;
}

.settings-item.danger .item-title {
  color: #e74c3c;
}

.item-content {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.item-icon {
  color: #666;
}

.settings-item.danger .item-icon {
  color: #e74c3c;
}

.item-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.item-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.item-desc {
  font-size: 12px;
  color: #999;
}

@media (max-width: 768px) {
  .page-header {
    padding: 16px;
  }
  
  .page-content {
    padding: 16px;
  }
  
  .settings-item {
    padding: 12px 16px;
  }
}
</style>
