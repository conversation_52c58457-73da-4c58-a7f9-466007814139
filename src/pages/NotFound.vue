<template>
  <div class="not-found">
    <div class="not-found-content">
      <n-icon :size="120" color="#ccc">
        <Search />
      </n-icon>
      <h1>404</h1>
      <p>页面未找到</p>
      <n-button type="primary" @click="$router.push('/home')">
        返回首页
      </n-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Search } from '@vicons/ionicons5'
</script>

<style scoped>
.not-found {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
}

.not-found-content {
  text-align: center;
}

.not-found-content h1 {
  font-size: 72px;
  font-weight: 600;
  color: #333;
  margin: 20px 0 10px;
}

.not-found-content p {
  font-size: 18px;
  color: #666;
  margin-bottom: 30px;
}
</style>
