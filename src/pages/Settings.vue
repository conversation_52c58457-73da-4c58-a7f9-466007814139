<template>
  <div class="settings-page">
    <div class="settings-header">
      <h1>设置</h1>
    </div>
    
    <div class="settings-content">
      <!-- 用户信息 -->
      <n-card title="用户信息" class="settings-card">
        <div class="user-info">
          <div class="user-avatar">
            <n-avatar :size="60" round>
              {{ userStore.user?.nickname?.charAt(0) || userStore.user?.username?.charAt(0) || 'U' }}
            </n-avatar>
          </div>
          <div class="user-details">
            <h3>{{ userStore.user?.nickname || userStore.user?.username }}</h3>
            <p>{{ userStore.user?.email }}</p>
          </div>
        </div>
      </n-card>
      
      <!-- 应用设置 -->
      <n-card title="应用设置" class="settings-card">
        <div class="setting-item">
          <div class="setting-label">
            <n-icon :size="20">
              <Moon />
            </n-icon>
            <span>深色模式</span>
          </div>
          <n-switch v-model:value="darkMode" @update:value="toggleDarkMode" />
        </div>

        <div class="setting-item">
          <div class="setting-label">
            <n-icon :size="20">
              <Notifications />
            </n-icon>
            <span>消息通知</span>
          </div>
          <n-switch v-model:value="notifications" />
        </div>
      </n-card>

      <!-- 数据管理 -->
      <n-card title="数据管理" class="settings-card">
        <div class="setting-item clickable" @click="$router.push('/settings/diet-types')">
          <div class="setting-label">
            <n-icon :size="20">
              <Restaurant />
            </n-icon>
            <span>饮食类型管理</span>
          </div>
          <n-icon :size="16" color="#ccc">
            <ChevronForward />
          </n-icon>
        </div>

        <div class="setting-item clickable" @click="$router.push('/settings/event-types')">
          <div class="setting-label">
            <n-icon :size="20">
              <Calendar />
            </n-icon>
            <span>事件类型管理</span>
          </div>
          <n-icon :size="16" color="#ccc">
            <ChevronForward />
          </n-icon>
        </div>

        <div class="setting-item clickable" @click="$router.push('/settings/data-sync')">
          <div class="setting-label">
            <n-icon :size="20">
              <Sync />
            </n-icon>
            <span>数据同步</span>
          </div>
          <n-icon :size="16" color="#ccc">
            <ChevronForward />
          </n-icon>
        </div>
      </n-card>
      
      <!-- 关于 -->
      <n-card title="关于应用" class="settings-card">
        <div class="about-info">
          <p><strong>版本:</strong> 1.0.0</p>
          <p><strong>开发者:</strong> Todo Calendar Team</p>
          <p><strong>描述:</strong> 一个功能丰富的待办日历应用</p>
        </div>
      </n-card>
      
      <!-- 账户操作 -->
      <n-card title="账户操作" class="settings-card">
        <div class="account-actions">
          <n-button type="error" block @click="handleLogout" :loading="logoutLoading">
            <template #icon>
              <n-icon>
                <LogOut />
              </n-icon>
            </template>
            退出登录
          </n-button>
        </div>
      </n-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { Moon, Notifications, LogOut, Restaurant, Calendar, ChevronForward, Sync } from '@vicons/ionicons5'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const userStore = useUserStore()

// 设置状态
const darkMode = ref(false)
const notifications = ref(true)
const logoutLoading = ref(false)

// 切换深色模式
const toggleDarkMode = (value: boolean) => {
  // 这里可以实现深色模式切换逻辑
  console.log('深色模式:', value)
}

// 处理登出
const handleLogout = async () => {
  logoutLoading.value = true
  try {
    await userStore.logout()
    router.push('/login')
  } catch (error) {
    console.error('登出失败:', error)
  } finally {
    logoutLoading.value = false
  }
}
</script>

<style scoped>
.settings-page {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 16px;
  padding-bottom: 80px; /* 为底部导航留空间 */
}

.settings-header {
  margin-bottom: 20px;
}

.settings-header h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.settings-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.settings-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.user-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.user-details h3 {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.user-details p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-item.clickable {
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.setting-item.clickable:hover {
  background-color: #f9f9f9;
}

.setting-label {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 16px;
  color: #333;
}

.about-info p {
  margin: 8px 0;
  color: #666;
  font-size: 14px;
}

.account-actions {
  padding: 8px 0;
}

/* 桌面端样式 */
@media (min-width: 768px) {
  .settings-page {
    max-width: 600px;
    margin: 0 auto;
    padding: 24px;
  }
  
  .settings-header h1 {
    font-size: 28px;
  }
}
</style>
