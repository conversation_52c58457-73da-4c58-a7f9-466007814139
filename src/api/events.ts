import { api } from './request'
import type { Event, CreateEventForm, DietEvent, EventCompletion, ApiResponse, PaginatedResponse } from '@/types'

/**
 * 事件相关API
 */
export const eventsApi = {
  // 获取事件列表
  getEvents(params?: {
    start_date?: string
    end_date?: string
    event_type_id?: number
    page?: number
    limit?: number
  }): Promise<ApiResponse<PaginatedResponse<Event>>> {
    return api.get('/events', { params })
  },

  // 获取单个事件
  getEvent(id: number): Promise<ApiResponse<Event>> {
    return api.get(`/events/${id}`)
  },

  // 创建事件
  createEvent(data: CreateEventForm): Promise<ApiResponse<Event>> {
    return api.post('/events', data)
  },

  // 更新事件
  updateEvent(id: number, data: Partial<Event>): Promise<ApiResponse<Event>> {
    return api.put(`/events/${id}`, data)
  },

  // 删除事件
  deleteEvent(id: number): Promise<ApiResponse<void>> {
    return api.delete(`/events/${id}`)
  },

  // 切换事件完成状态
  toggleCompletion(id: number, isCompleted: boolean): Promise<ApiResponse<Event>> {
    return api.patch(`/events/${id}/completion`, { is_completed: isCompleted })
  },

  // 批量操作事件
  batchUpdate(ids: number[], data: Partial<Event>): Promise<ApiResponse<Event[]>> {
    return api.patch('/events/batch', { ids, data })
  }
}

/**
 * 饮食事件相关API
 */
export const dietEventsApi = {
  // 获取饮食事件详情
  getDietEvent(eventId: number): Promise<ApiResponse<DietEvent>> {
    return api.get(`/events/${eventId}/diet`)
  },

  // 创建或更新饮食事件详情
  saveDietEvent(eventId: number, data: Partial<DietEvent>): Promise<ApiResponse<DietEvent>> {
    return api.put(`/events/${eventId}/diet`, data)
  },

  // 删除饮食事件详情
  deleteDietEvent(eventId: number): Promise<ApiResponse<void>> {
    return api.delete(`/events/${eventId}/diet`)
  }
}

/**
 * 事件完成记录相关API
 */
export const eventCompletionsApi = {
  // 获取事件完成记录
  getCompletions(eventId: number): Promise<ApiResponse<EventCompletion[]>> {
    return api.get(`/events/${eventId}/completions`)
  },

  // 添加完成记录
  addCompletion(eventId: number, data: {
    completion_date: string
    notes?: string
  }): Promise<ApiResponse<EventCompletion>> {
    return api.post(`/events/${eventId}/completions`, data)
  },

  // 删除完成记录
  deleteCompletion(eventId: number, completionId: number): Promise<ApiResponse<void>> {
    return api.delete(`/events/${eventId}/completions/${completionId}`)
  }
}
