import { api } from './request'
import type { EventType, DietType, ApiResponse } from '@/types'

/**
 * 事件类型相关API
 */
export const eventTypesApi = {
  // 获取所有事件类型
  getEventTypes(): Promise<ApiResponse<EventType[]>> {
    return api.get('/event-types')
  },

  // 获取单个事件类型
  getEventType(id: number): Promise<ApiResponse<EventType>> {
    return api.get(`/event-types/${id}`)
  },

  // 创建事件类型
  createEventType(data: Omit<EventType, 'id' | 'created_at'>): Promise<ApiResponse<EventType>> {
    return api.post('/event-types', data)
  },

  // 更新事件类型
  updateEventType(id: number, data: Partial<EventType>): Promise<ApiResponse<EventType>> {
    return api.put(`/event-types/${id}`, data)
  },

  // 删除事件类型
  deleteEventType(id: number): Promise<ApiResponse<void>> {
    return api.delete(`/event-types/${id}`)
  }
}

/**
 * 饮食类型相关API
 */
export const dietTypesApi = {
  // 获取所有饮食类型
  getDietTypes(): Promise<ApiResponse<DietType[]>> {
    return api.get('/diet-types')
  },

  // 获取单个饮食类型
  getDietType(id: number): Promise<ApiResponse<DietType>> {
    return api.get(`/diet-types/${id}`)
  },

  // 创建饮食类型
  createDietType(data: Omit<DietType, 'id' | 'created_at'>): Promise<ApiResponse<DietType>> {
    return api.post('/diet-types', data)
  },

  // 更新饮食类型
  updateDietType(id: number, data: Partial<DietType>): Promise<ApiResponse<DietType>> {
    return api.put(`/diet-types/${id}`, data)
  },

  // 删除饮食类型
  deleteDietType(id: number): Promise<ApiResponse<void>> {
    return api.delete(`/diet-types/${id}`)
  }
}
