<template>
  <div class="mobile-layout">
    <!-- 主内容区域 -->
    <div class="main-content">
      <router-view />
    </div>
    
    <!-- 底部导航栏 -->
    <div class="bottom-nav">
      <div class="nav-items">
        <div
          v-for="item in navItems"
          :key="item.name"
          class="nav-item"
          :class="{ active: isActive(item.path) }"
          @click="handleNavClick(item)"
        >
          <n-icon :size="24" class="nav-icon">
            <component :is="getIcon(item.icon)" />
          </n-icon>
          <span class="nav-label">{{ item.label }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { 
  Home as HomeIcon,
  Calendar as CalendarIcon,
  Add as AddIcon,
  Settings as SettingsIcon,
  BookOutline as BookIcon
} from '@vicons/ionicons5'
import type { NavItem } from '@/types'

const router = useRouter()
const route = useRoute()

// 导航项配置
const navItems: NavItem[] = [
  { name: 'home', icon: 'home', path: '/home', label: '首页' },
  { name: 'reserved1', icon: 'book', path: '/reserved1', label: '预留' },
  { name: 'add', icon: 'add', path: '/events/create', label: '添加' },
  { name: 'reserved2', icon: 'calendar', path: '/reserved2', label: '预留' },
  { name: 'settings', icon: 'settings', path: '/settings', label: '设置' }
]

// 获取图标组件
const getIcon = (iconName: string) => {
  const iconMap: Record<string, any> = {
    home: HomeIcon,
    calendar: CalendarIcon,
    add: AddIcon,
    settings: SettingsIcon,
    book: BookIcon
  }
  return iconMap[iconName] || HomeIcon
}

// 检查是否为当前路由
const isActive = (path: string) => {
  if (path === '/home') {
    return route.path === '/' || route.path === '/home'
  }
  return route.path.startsWith(path)
}

// 处理导航点击
const handleNavClick = (item: NavItem) => {
  if (item.name === 'add') {
    // 添加按钮特殊处理
    router.push('/events/create')
  } else if (item.path.startsWith('/reserved')) {
    // 预留功能暂时不跳转
    window.$message?.info('功能开发中...')
  } else {
    router.push(item.path)
  }
}
</script>

<style scoped>
.mobile-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.main-content {
  flex: 1;
  overflow-y: auto;
  padding-bottom: env(safe-area-inset-bottom);
}

.bottom-nav {
  background: white;
  border-top: 1px solid #e0e0e0;
  padding: 8px 0;
  padding-bottom: calc(8px + env(safe-area-inset-bottom));
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
}

.nav-items {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 4px 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 8px;
  min-width: 60px;
}

.nav-item:hover {
  background-color: #f0f0f0;
}

.nav-item.active {
  color: #667eea;
}

.nav-item.active .nav-icon {
  color: #667eea;
}

.nav-icon {
  margin-bottom: 2px;
  color: #666;
  transition: color 0.2s ease;
}

.nav-label {
  font-size: 12px;
  color: #666;
  transition: color 0.2s ease;
}

.nav-item.active .nav-label {
  color: #667eea;
  font-weight: 500;
}

/* 添加按钮特殊样式 */
.nav-item[data-name="add"] {
  position: relative;
}

.nav-item[data-name="add"] .nav-icon {
  background: #667eea;
  color: white;
  border-radius: 50%;
  padding: 8px;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.nav-item[data-name="add"] .nav-label {
  color: #667eea;
  font-weight: 500;
}
</style>
