<template>
  <div class="desktop-layout">
    <!-- 侧边栏 -->
    <div class="sidebar" :class="{ collapsed: appStore.sidebarCollapsed }">
      <div class="sidebar-header">
        <div class="logo">
          <n-icon :size="32" color="#667eea">
            <Calendar />
          </n-icon>
          <span v-if="!appStore.sidebarCollapsed" class="logo-text">待办日历</span>
        </div>
        
        <n-button
          quaternary
          circle
          @click="appStore.toggleSidebar"
        >
          <n-icon :size="20">
            <Menu />
          </n-icon>
        </n-button>
      </div>
      
      <div class="sidebar-content">
        <n-menu
          :value="activeKey"
          :options="menuOptions"
          :collapsed="appStore.sidebarCollapsed"
          :collapsed-width="64"
          :collapsed-icon-size="22"
          @update:value="handleMenuSelect"
        />
      </div>
      
      <div class="sidebar-footer">
        <div class="user-info" v-if="userStore.user">
          <n-avatar
            :size="appStore.sidebarCollapsed ? 32 : 40"
            :src="userStore.user.avatar_url"
            :fallback-src="'/default-avatar.png'"
          >
            {{ userStore.user.nickname?.[0] || userStore.user.username[0] }}
          </n-avatar>
          <div v-if="!appStore.sidebarCollapsed" class="user-details">
            <div class="username">{{ userStore.user.nickname || userStore.user.username }}</div>
            <div class="email">{{ userStore.user.email }}</div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 主内容区域 -->
    <div class="main-content">
      <router-view />
    </div>
  </div>
</template>

<script setup lang="ts">
import { 
  Calendar,
  Menu,
  Home as HomeIcon,
  Add as AddIcon,
  Settings as SettingsIcon,
  List as ListIcon
} from '@vicons/ionicons5'
import type { MenuOption } from 'naive-ui'
import { useAppStore } from '@/stores/app'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const route = useRoute()
const appStore = useAppStore()
const userStore = useUserStore()

// 当前激活的菜单项
const activeKey = computed(() => {
  const path = route.path
  if (path === '/' || path === '/home') return 'home'
  if (path.startsWith('/events')) return 'events'
  if (path.startsWith('/settings')) return 'settings'
  return 'home'
})

// 菜单选项
const menuOptions: MenuOption[] = [
  {
    label: '首页',
    key: 'home',
    icon: () => h(HomeIcon)
  },
  {
    label: '事件管理',
    key: 'events',
    icon: () => h(ListIcon),
    children: [
      {
        label: '事件列表',
        key: 'events-list'
      },
      {
        label: '创建事件',
        key: 'events-create'
      }
    ]
  },
  {
    label: '快速添加',
    key: 'quick-add',
    icon: () => h(AddIcon)
  },
  {
    label: '设置',
    key: 'settings',
    icon: () => h(SettingsIcon)
  }
]

// 处理菜单选择
const handleMenuSelect = (key: string) => {
  switch (key) {
    case 'home':
      router.push('/home')
      break
    case 'events-list':
      router.push('/events')
      break
    case 'events-create':
    case 'quick-add':
      router.push('/events/create')
      break
    case 'settings':
      router.push('/settings')
      break
  }
}
</script>

<style scoped>
.desktop-layout {
  height: 100vh;
  display: flex;
  background-color: #f5f5f5;
}

.sidebar {
  width: 260px;
  background: white;
  border-right: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
}

.sidebar.collapsed {
  width: 64px;
}

.sidebar-header {
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-text {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.sidebar-content {
  flex: 1;
  padding: 16px 0;
  overflow-y: auto;
}

.sidebar-footer {
  padding: 16px;
  border-top: 1px solid #e0e0e0;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-details {
  flex: 1;
  min-width: 0;
}

.username {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.email {
  font-size: 12px;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.main-content {
  flex: 1;
  overflow-y: auto;
  background-color: #f5f5f5;
}

/* 响应式调整 */
@media (max-width: 1024px) {
  .sidebar {
    width: 200px;
  }
  
  .sidebar.collapsed {
    width: 64px;
  }
}
</style>
