import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import pinia from './stores'
import VCalendar from 'v-calendar'
import { initSyncService } from './services/syncService'

// 样式
import './assets/styles/main.css'
import 'v-calendar/style.css'

// 创建应用
const app = createApp(App)

// 使用插件
app.use(router)
app.use(pinia)
app.use(VCalendar, {})

// 初始化同步服务
initSyncService()

// 挂载应用
app.mount('#app')
