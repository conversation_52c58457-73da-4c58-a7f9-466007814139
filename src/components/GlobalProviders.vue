<template>
  <!-- 这个组件不渲染任何内容，只用于初始化全局状态 -->
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useMessage, useDialog, useNotification, useLoadingBar } from 'naive-ui'

// 初始化应用
onMounted(async () => {
  // 全局挂载消息组件
  window.$message = useMessage()
  window.$dialog = useDialog()
  window.$notification = useNotification()
  window.$loadingBar = useLoadingBar()

  // 初始化用户状态
  try {
    // 动态导入以避免循环依赖
    const { useUserStore } = await import('../stores/user')
    const userStore = useUserStore()
    await userStore.init()
  } catch (error) {
    console.error('Failed to initialize user store:', error)
  }
})
</script>
