<template>
  <div class="test-info">
    <n-card title="测试账号信息" size="small">
      <div class="test-accounts">
        <div class="account-item">
          <h4>普通用户</h4>
          <p><strong>用户名:</strong> testuser</p>
          <p><strong>密码:</strong> 123456</p>
        </div>
        
        <div class="account-item">
          <h4>管理员</h4>
          <p><strong>用户名:</strong> admin</p>
          <p><strong>密码:</strong> 123456</p>
        </div>
      </div>
      
      <n-divider />
      
      <div class="features">
        <h4>功能说明</h4>
        <ul>
          <li>📱 响应式设计 - 自动适配移动端和桌面端</li>
          <li>📅 日历功能 - VCalendar集成，事件颜色标识</li>
          <li>🍽️ 饮食规划 - 6种预设饮食类型</li>
          <li>📝 通用事件 - 记事本功能</li>
          <li>👤 用户系统 - 注册登录，个人资料</li>
        </ul>
      </div>
    </n-card>
  </div>
</template>

<script setup lang="ts">
// 组件逻辑
</script>

<style scoped>
.test-info {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 300px;
  z-index: 1000;
  opacity: 0.9;
}

.test-accounts {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.account-item {
  padding: 12px;
  background: #f5f5f5;
  border-radius: 6px;
}

.account-item h4 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 14px;
}

.account-item p {
  margin: 4px 0;
  font-size: 12px;
  color: #666;
}

.features {
  margin-top: 16px;
}

.features h4 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 14px;
}

.features ul {
  margin: 0;
  padding-left: 16px;
  font-size: 12px;
  color: #666;
}

.features li {
  margin: 4px 0;
}

@media (max-width: 768px) {
  .test-info {
    position: relative;
    top: auto;
    right: auto;
    width: 100%;
    margin: 16px;
  }
}
</style>
