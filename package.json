{"name": "vite-todo-list", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@types/js-cookie": "^3.0.6", "@vicons/ionicons5": "^0.13.0", "@vueuse/core": "^13.9.0", "axios": "^1.11.0", "js-cookie": "^3.0.5", "naive-ui": "^2.42.0", "pinia": "^3.0.3", "v-calendar": "^3.1.2", "vue": "^3.5.18", "vue-router": "^4.5.1"}, "devDependencies": {"@types/node": "^24.3.1", "@vitejs/plugin-vue": "^6.0.1", "@vue/tsconfig": "^0.7.0", "typescript": "~5.8.3", "unplugin-auto-import": "^20.1.0", "unplugin-vue-components": "^29.0.0", "vite": "^7.1.2", "vue-tsc": "^3.0.5"}}