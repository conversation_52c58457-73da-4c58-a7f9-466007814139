-- 待办列表应用数据库设计
-- 创建数据库
CREATE DATABASE IF NOT EXISTS todo_calendar DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE todo_calendar;

-- 用户表
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    nickname VARCHAR(50),
    avatar_url VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE
);

-- 饮食类型表（预设数据）
CREATE TABLE diet_types (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL,
    name_en VARCHAR(50) NOT NULL,
    description TEXT,
    food_types JSON, -- 包含的食物类型
    nutrition_info JSON, -- 营养信息
    characteristics TEXT, -- 特点
    benefits TEXT, -- 益处
    suitable_people TEXT, -- 适合人群
    -- 建议食用周期相关字段
    suggested_frequency ENUM('daily', 'weekly', 'monthly') DEFAULT 'daily', -- 建议频率
    suggested_interval INT DEFAULT 1, -- 建议间隔（配合频率使用）
    suggested_duration_days INT, -- 建议持续天数（如：持续30天）
    suggested_frequency_per_week INT, -- 每周建议次数（如：每周2次）
    cycle_description TEXT, -- 周期描述（如：持续周期或频率说明）
    is_system BOOLEAN DEFAULT FALSE, -- 是否为系统预设类型
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 事件类型表
CREATE TABLE event_types (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL,
    name_en VARCHAR(50) NOT NULL,
    description TEXT,
    icon VARCHAR(50), -- 图标名称
    color VARCHAR(7), -- 颜色代码
    form_config JSON, -- 表单配置信息
    is_system BOOLEAN DEFAULT FALSE, -- 是否为系统预设类型
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 事件表
CREATE TABLE events (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    event_type_id INT NOT NULL,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    start_date DATE NOT NULL,
    end_date DATE,
    start_time TIME,
    end_time TIME,
    duration_type ENUM('single', 'daily', 'weekly', 'monthly') DEFAULT 'single',
    duration_value INT DEFAULT 1, -- 持续天数/周数/月数
    recurrence_pattern JSON, -- 重复模式配置
    is_completed BOOLEAN DEFAULT FALSE,
    completed_at TIMESTAMP NULL,
    priority ENUM('low', 'medium', 'high') DEFAULT 'medium',
    extra_data JSON, -- 额外数据（根据事件类型存储不同信息）
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (event_type_id) REFERENCES event_types(id)
);

-- 饮食规划事件详情表
CREATE TABLE diet_events (
    id INT PRIMARY KEY AUTO_INCREMENT,
    event_id INT NOT NULL,
    diet_type_id INT,
    meal_type ENUM('breakfast', 'lunch', 'dinner', 'snack') NOT NULL,
    planned_foods JSON, -- 计划的食物
    actual_foods JSON, -- 实际的食物
    calories_planned INT,
    calories_actual INT,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE,
    FOREIGN KEY (diet_type_id) REFERENCES diet_types(id)
);

-- 事件完成记录表（用于跟踪重复事件的完成情况）
CREATE TABLE event_completions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    event_id INT NOT NULL,
    completion_date DATE NOT NULL,
    completed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    notes TEXT,
    UNIQUE KEY unique_event_date (event_id, completion_date),
    FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX idx_events_user_date ON events(user_id, start_date);
CREATE INDEX idx_events_type ON events(event_type_id);
CREATE INDEX idx_event_completions_date ON event_completions(completion_date);
CREATE INDEX idx_diet_events_event ON diet_events(event_id);
